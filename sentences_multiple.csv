content,url
"Bienvenue dans mon guide exhaustif du vocabulaire de l’intelligence artificielle, l’IA.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ce domaine technologique, en pleine expansion, est porteur de transformations majeures dans de nombreux secteurs.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Cependant, il s’accompagne d’un jargon spécifique qui peut parfois sembler complexe.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’objectif de ce lexique est de clarifier les termes et concepts clés de l’IA, afin de vous offrir une meilleure compréhension de ses mécanismes, de ses applications et de ses enjeux.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Que vous soyez novice curieux, étudiant, professionnel cherchant à approfondir vos connaissances, ou simplement désireux de mieux appréhender les discussions actuelles sur l’IA, ce glossaire alphabétique est conçu pour vous.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Nous aborderons chaque terme de manière factuelle et informative, en fournissant les équivalents anglais lorsque cela est pertinent, pour faciliter votre navigation dans cet univers fascinant.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Agent intelligent (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Intelligent agent)
Un agent intelligent est une entité logicielle ou matérielle capable de percevoir son environnement à travers des capteurs, de traiter ces informations, et d’agir sur cet environnement via des effecteurs de manière autonome pour atteindre des objectifs spécifiques.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il peut apprendre de ses expériences pour améliorer ses performances.
Algorithme (Algorithm)
Un algorithme représente une séquence d’instructions ou de règles logiques, finies et non ambiguës, conçue pour résoudre un problème spécifique ou accomplir une tâche à partir d’un état initial et de données d’entrée.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Dans le contexte de l’intelligence artificielle, les algorithmes sont fondamentaux car ils dictent la manière dont les systèmes analysent les données, apprennent et prennent des décisions.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Annotation (Annotation / Labeling)
L’annotation de données en intelligence artificielle est le processus qui consiste à étiqueter ou à catégoriser manuellement des données brutes (images, textes, sons, etc.) pour les rendre compréhensibles et utilisables par les algorithmes d’apprentissage automatique, en particulier pour l’apprentissage supervisé.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
On parle aussi de Labellisation.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Anthropomorphisation (de l’IA) (Anthropomorphism)
L’anthropomorphisation de l’IA fait référence à la tendance à attribuer des caractéristiques, des émotions, des intentions ou une conscience humaines à des systèmes d’intelligence artificielle.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Bien que cela puisse faciliter la compréhension de certains concepts, il est important de distinguer les capacités réelles des machines de ces projections humaines.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Apprentissage actif (Active learning)
L’apprentissage actif est une technique",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
d’apprentissage automatique où l’algorithme est capable de choisir de manière interactive les données à partir desquelles il apprend.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Généralement, il sélectionne les exemples les plus informatifs à faire annoter par un expert humain, optimisant ainsi le processus d’apprentissage avec un minimum de données étiquetées.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Apprentissage automatique (Machine learning)
L’apprentissage automatique, ou Machine Learning (ML), est un sous-domaine de l’intelligence artificielle qui donne aux systèmes informatiques la capacité d’apprendre à partir de données, sans être explicitement programmés pour chaque tâche.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Les algorithmes de ML identifient des motifs (patterns) dans les données pour faire des prédictions ou prendre des décisions.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Apprentissage,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
auto-supervisé (Self-supervised learning),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’apprentissage auto-supervisé est une méthode d’apprentissage automatique où le modèle génère lui-même les étiquettes à partir des données d’entrée non étiquetées.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il apprend en résolvant des tâches prétextes, par exemple en prédisant une partie masquée d’une donnée à partir du reste de cette donnée.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Apprentissage continu (Continual learning / Lifelong learning)
L’apprentissage continu désigne la capacité d’un système d’IA à apprendre de manière incrémentale à partir d’un flux continu de nouvelles données, après son déploiement initial.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’objectif est d’adapter et d’améliorer le modèle au fil du temps sans oublier les connaissances précédemment acquises.
Apprentissage fédéré (Federated learning)
L’apprentissage fédéré est une technique d’apprentissage automatique qui permet d’entraîner des modèles d’IA sur des ensembles de données distribués (par exemple, sur les appareils des utilisateurs) sans que ces données ne quittent leur emplacement d’origine.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Seuls les modèles ou les mises à jour des modèles sont partagés, préservant ainsi la confidentialité des données.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Apprentissage non supervisé (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Unsupervised learning)
L’apprentissage non supervisé est un type d’apprentissage automatique où l’algorithme est entraîné sur des données non étiquetées.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le système tente de découvrir par lui-même des structures cachées, des regroupements (clustering) ou des associations dans les données sans indication explicite sur les résultats attendus.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Apprentissage par renforcement (Reinforcement learning)
L’apprentissage par renforcement est une méthode d’apprentissage automatique où un agent apprend à prendre des décisions en interagissant avec un environnement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’agent reçoit des récompenses ou des punitions en fonction de ses actions, ce qui l’aide à développer une stratégie (politique) pour maximiser la récompense cumulée sur le long terme.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Apprentissage par renforcement et rétroaction humaine (RLHF,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"– Reinforcement learning from human feedback)
L’apprentissage par renforcement avec rétroaction humaine (RLHF) est une technique qui intègre des évaluations humaines dans le processus d’apprentissage par renforcement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les humains fournissent des retours (par exemple, en classant les réponses d’un modèle) qui sont utilisés pour affiner la fonction de récompense de l’agent, améliorant ainsi son comportement, notamment pour les grands modèles de langage.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Apprentissage par transfert (Transfer learning)
L’apprentissage par transfert est une technique",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
d’apprentissage automatique où un modèle développé et entraîné pour une tâche est réutilisé comme point de départ pour un modèle sur une tâche différente mais connexe.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Cela est particulièrement utile lorsque les données disponibles pour la nouvelle tâche sont limitées.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Apprentissage profond (Deep learning)
L’apprentissage profond, ou Deep Learning, est un sous-domaine de l’apprentissage automatique qui utilise des réseaux de neurones artificiels comportant de multiples couches (d’où le terme « profond »).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ces couches permettent d’analyser les données à différents niveaux d’abstraction, rendant ces modèles performants pour des tâches complexes comme la reconnaissance d’images ou le traitement du langage naturel.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Apprentissage supervisé (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Supervised learning)
L’apprentissage supervisé est un type d’apprentissage automatique où l’algorithme est entraîné sur un ensemble de données étiquetées.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Chaque exemple de donnée d’entraînement est accompagné d’une « réponse » ou « sortie » correcte attendue.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le modèle apprend à prédire la sortie pour de nouvelles données en se basant sur les exemples vus.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Attaque adverse / Attaque,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
par exemples contradictoires (Adversarial examples attack),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Une attaque adverse, ou attaque par exemples contradictoires (Adversarial Examples Attack), consiste à introduire des perturbations subtiles et souvent imperceptibles par l’homme dans les données d’entrée d’un modèle d’IA (par exemple, une image).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ces perturbations sont conçues pour tromper le modèle et le conduire à produire une sortie incorrecte ou non désirée, tout en restant plausible.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Attaque par empoisonnement (Data poisoning attack),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Une attaque par empoisonnement de données (Data Poisoning Attack) vise à corrompre la phase d’entraînement d’un modèle d’IA en introduisant des données malveillantes ou manipulées dans l’ensemble de données d’apprentissage.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’objectif est de dégrader les performances du modèle, d’introduire des biais spécifiques ou de créer des « portes dérobées » (backdoors).
Attaque par exfiltration de modèle (Model evasion/extraction attack)
L’attaque par exfiltration de modèle (également appelée vol de modèle ou Model Extraction",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Attack),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
cherche à obtenir une copie fonctionnelle du modèle d’IA ou à en déduire ses paramètres et son architecture.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’attaquant interroge le modèle avec un grand nombre de requêtes et observe les sorties pour reconstituer son fonctionnement interne.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Attaque par inférence d’appartenance (Membership inference attack),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Une attaque par inférence d’appartenance (Membership Inference Attack) a pour but de déterminer si une donnée spécifique (par exemple, les informations d’un individu) a été utilisée lors de l’entraînement d’un modèle d’IA.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ce type d’attaque soulève des préoccupations importantes en matière de confidentialité des données.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Attaque par inversion de modèle (Model inversion attack),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’attaque par inversion de modèle (Model Inversion,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Attack),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
vise à reconstruire tout ou partie des données d’entraînement sensibles à partir d’un accès au modèle d’IA entraîné.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Par exemple, à partir d’un modèle de reconnaissance faciale, on pourrait tenter de reconstituer des images de visages présents dans les données d’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Attaque par reconstruction (Reconstruction attack),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Une attaque par reconstruction cherche à reconstituer, souvent de manière approximative, des données d’entraînement originales ou des informations sensibles à partir d’un modèle d’IA entraîné ou de ses sorties.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle est étroitement liée à l’attaque par inversion de modèle.
Augmentation de données (IA) (Data augmentation)
L’augmentation de données (Data Augmentation) est une technique utilisée pour accroître artificiellement la taille et la diversité de l’ensemble de données d’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle consiste à créer de nouvelles données d’entraînement en appliquant des transformations mineures aux données existantes (par exemple, rotation ou recadrage d’images, synonymes dans un texte).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Automatisation (par l’IA) (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
AI automation,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’automatisation par l’IA désigne l’utilisation de systèmes d’intelligence artificielle pour exécuter des tâches qui nécessitaient auparavant une intervention humaine.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Cela peut aller de tâches répétitives à des processus de décision plus complexes, dans divers secteurs d’activité.
Biais (IA) (AI bias)
Un biais en IA (AI Bias) se réfère à des résultats ou des prédictions systématiquement erronés ou inéquitables produits par un système d’intelligence artificielle.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ces biais peuvent provenir des données d’entraînement (si elles reflètent des préjugés existants dans la société), de la conception de l’algorithme, ou de la manière dont le modèle est déployé et utilisé.
Big data (Mégadonnées) (Big data)
Le Big Data, ou Mégadonnées, désigne des ensembles de données extrêmement volumineux, variés et générés à grande vitesse, qui dépassent les capacités de traitement des outils de gestion de bases de données traditionnels.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’analyse du Big Data est souvent un prérequis pour entraîner des modèles d’IA performants.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Boucle de rétroaction (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Feedback loop),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Une boucle de rétroaction, dans le contexte de l’IA, est un processus où la sortie d’un système est réutilisée comme entrée pour influencer son comportement futur.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est un concept clé en apprentissage par renforcement et pour les systèmes d’IA agentiques, leur permettant d’apprendre et de s’adapter continuellement.
Bulle de filtre (Filter bubble)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Une bulle de filtre est un état d’isolement intellectuel qui peut résulter de recommandations personnalisées par des algorithmes.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Lorsque ces algorithmes ne montrent à un utilisateur que des contenus qui correspondent à ses opinions passées ou à ses préférences, cela peut limiter son exposition à des points de vue divergents et renforcer ses propres croyances.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Calcul multipartite sécurisé (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Secure multi-party computation,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
– SMPC),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le calcul multipartite sécurisé (SMPC) est une technique cryptographique qui permet à plusieurs parties de calculer conjointement une fonction sur leurs données privées sans révéler ces données les unes aux autres, ni à une tierce partie.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est pertinent pour l’entraînement d’IA collaboratif et respectueux de la vie privée.
Caractéristique (IA) (Feature)
Une caractéristique, ou feature en anglais, est une variable ou un attribut individuel et mesurable d’une donnée d’entrée, utilisée par un modèle d’IA pour faire des prédictions ou prendre des décisions.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le choix et l’ingénierie des caractéristiques (feature engineering) sont des étapes importantes dans la construction d’,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"un modèle performant.
Catastrophisme de l’IA (AI doomerism)
Le catastrophisme de l’IA (AI Doomerism) désigne un courant de pensée qui met l’accent sur les risques existentiels potentiels et les scénarios les plus négatifs associés au développement de l’intelligence artificielle, notamment l’émergence d’une superintelligence incontrôlable.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Chatbot (Agent conversationnel) (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Chatbot / Conversational agent)
Un chatbot, ou agent conversationnel, est un programme informatique conçu pour simuler une conversation humaine (écrite ou orale) avec les utilisateurs.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Les chatbots modernes utilisent souvent des techniques de traitement du langage naturel et d’IA pour comprendre les requêtes et fournir des réponses pertinentes.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Chiffrement homomorphe (Homomorphic encryption),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le chiffrement homomorphe est une forme de cryptage qui permet d’effectuer des calculs directement sur des données chiffrées sans avoir besoin de les déchiffrer au préalable.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le résultat de ces calculs reste chiffré et ne peut être lu qu’après déchiffrement.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est une technologie prometteuse pour la confidentialité dans l’IA.
Classification (IA) (Classification)
La classification en IA est une tâche d’apprentissage supervisé qui consiste à attribuer une catégorie (ou classe) prédéfinie à une nouvelle observation, en se basant sur les caractéristiques de cette observation et sur ce que le modèle a appris à partir de données d’entraînement étiquetées.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Par exemple, classer un email comme « spam » ou « non-spam ».",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Cloud computing (pour l’IA) (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Cloud computing),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le Cloud Computing (informatique en nuage) joue un rôle essentiel pour l’IA en fournissant des ressources de calcul puissantes (comme les GPU et TPU), des capacités de stockage de données massives, et des plateformes spécialisées pour entraîner et déployer des modèles d’intelligence artificielle à grande échelle, souvent de manière flexible et à la demande.
Codage agentique (Agentic coding)
Le codage agentique fait référence à l’utilisation de systèmes d’IA, en particulier des agents intelligents, pour assister ou automatiser des tâches de développement logiciel, comme l’écriture de code, la correction de bugs, ou la conception d’architecture logicielle.
Confabulation (IA) (Confabulation)
La confabulation en IA est un terme souvent utilisé de manière interchangeable avec « hallucination ».",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il décrit la tendance des modèles d’IA générative à produire des informations incorrectes, inventées ou incohérentes, tout en les présentant comme factuelles et plausibles, sans avoir de base dans les données d’entraînement pour ces affirmations spécifiques.
Confidentialité différentielle (Differential privacy)
La confidentialité différentielle est un cadre mathématique qui permet d’analyser des ensembles de données et de publier des informations agrégées tout en limitant la divulgation d’informations sur les individus présents dans ces données.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Elle ajoute un « bruit » contrôlé aux données ou aux résultats pour protéger la vie privée.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Contrôle itératif de l’apprentissage (Défense RONI,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"– Reject on negative impact)
Le contrôle itératif de l’apprentissage est une approche visant à améliorer la robustesse et la fiabilité des modèles d’IA.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"La défense RONI (Reject On Negative Impact), par exemple, consiste à évaluer l’impact de chaque donnée d’entraînement sur les performances du modèle et à rejeter celles qui ont un impact négatif ou qui pourraient introduire des vulnérabilités.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Couche de neurones (Layer)
Dans un réseau de neurones artificiels",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
", une couche (layer) est un ensemble de neurones qui effectuent collectivement une transformation sur les données qu’ils reçoivent de la couche précédente.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Les réseaux de neurones profonds sont caractérisés par la présence de multiples couches cachées entre la couche d’entrée et la couche de sortie.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Critère d’arrêt (IA) (Stopping criterion),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un critère d’arrêt (stopping criterion) est une condition prédéfinie qui, lorsqu’elle est remplie, met fin au processus d’entraînement d’un modèle d’IA.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Cela peut être un nombre maximal d’itérations, l’atteinte d’un certain niveau de performance sur un ensemble de validation, ou une stagnation de l’amélioration de la fonction de perte.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Dérive des données (Data drift),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
La dérive des données (Data Drift) se produit lorsque les caractéristiques statistiques des données d’entrée sur lesquelles un modèle d’IA est utilisé en production diffèrent significativement de celles des données sur lesquelles il a été entraîné.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Cela peut entraîner une dégradation des performances du modèle au fil du temps.
Dérive du modèle (Model drift)
La dérive du modèle (Model Drift) désigne la perte de performance ou d’adéquation d’un modèle d’IA au fil du temps.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Cela peut être dû à une dérive des données (les données réelles changent) ou à des changements dans les relations sous-jacentes que le modèle avait apprises (les concepts évoluent).,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Désapprentissage,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
machine (Machine unlearning),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le désapprentissage machine (Machine Unlearning) est le processus qui vise à supprimer l’influence de données spécifiques d’,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"un modèle d’IA déjà entraîné, sans avoir à réentraîner complètement le modèle à partir de zéro.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
C’est important pour le respect du droit à l’oubli et la correction des erreurs.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Domaine d’,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
emploi (IA) (Domain of applicability),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le domaine d’emploi en IA décrit le contexte spécifique, les conditions, les types de données et la population cible pour lesquels un système d’intelligence artificielle a été conçu, entraîné et validé.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Utiliser un modèle en dehors de son domaine d’emploi,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
peut entraîner des performances imprévisibles ou erronées.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Donnée brute (IA) (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Raw data)
Une donnée brute (raw data) en IA est une information collectée directement à sa source, avant qu’elle n’ait subi un quelconque traitement, nettoyage, transformation ou annotation.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est le point de départ pour la préparation des données en vue de l’entraînement d’un modèle.
Donnée d’entrée (IA) (Input data)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Une donnée d’entrée (input data) est l’information fournie à un système d’IA, que ce soit pendant la phase d’entraînement pour apprendre, ou pendant la phase de production (inférence) pour que le modèle génère une prédiction ou une action.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Donnée de sortie (IA) (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Output data)
Une donnée de sortie (output data) est le résultat produit par un système d’IA après avoir traité les données d’entrée.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Cela peut être une prédiction, une classification, une recommandation, du texte généré, une image, ou une action à effectuer.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Données (Data),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les données (Data) sont l’ensemble des informations, faits, chiffres, observations ou mesures",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"qui sont collectés, stockés et traités.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"En intelligence artificielle, les données sont cruciales : elles servent à entraîner les modèles, à les tester et à leur permettre de prendre des décisions ou de faire des prédictions.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Échantillon (IA) (Sample),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un échantillon (sample) en IA est une observation individuelle ou un sous-ensemble de données sélectionné à partir d’un ensemble de données plus large (la population).,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les échantillons sont utilisés pour l’entraînement, la validation et le test des modèles.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Enchainement des actions (Action sequencing)
L’enchainement des actions (",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Action Sequencing) est une capacité clé des systèmes d’IA agentiques.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle leur permet de planifier et d’exécuter une série d’actions interdépendantes de manière autonome pour atteindre un objectif complexe, en adaptant potentiellement la séquence en fonction des résultats intermédiaires.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ensemble d’entraînement (ou Ensemble d’apprentissage) (Training set)
L’ensemble",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
d’entraînement (Training Set) est la portion des données utilisée pour « apprendre » les paramètres d’un modèle d’intelligence artificielle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le modèle identifie des motifs et des relations dans cet ensemble pour pouvoir généraliser à de nouvelles données.
Ensemble de test (IA) (Test set)
L’ensemble de test (Test Set) est une portion des données, distincte de l’ensemble d’entraînement et de validation, utilisée pour évaluer la performance finale et la capacité de généralisation d’un modèle d’IA une fois qu’il a été complètement entraîné et optimisé.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ensemble de validation (IA) (Validation set)
L’ensemble de validation (Validation Set) est une portion des données, distincte de l’ensemble d’entraînement, utilisée pendant le processus d’entraînement pour ajuster les hyperparamètres du modèle et pour évaluer ses performances de manière intermédiaire, afin d’éviter le surapprentissage.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Entraînement (IA) (ou Apprentissage) (Training)
L’entraînement (Training), ou apprentissage",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
", est le processus par lequel un modèle d’intelligence artificielle ajuste ses paramètres internes en se basant sur un ensemble de données d’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’objectif est que le modèle apprenne à effectuer une tâche spécifique (par exemple, classification, prédiction) avec une bonne performance.
Environnement d’exécution de confiance (TEE",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
– Trusted execution environment),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un environnement d’,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
exécution de confiance (TEE),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
est une zone sécurisée et isolée au sein du processeur d’un ordinateur.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il garantit que le code et les données qui y sont chargés sont protégés en termes de confidentialité et d’intégrité, même contre des logiciels malveillants s’exécutant sur le même système.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
C’est utile pour sécuriser les modèles d’IA et les données sensibles.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Estimation bayésienne (ou Inférence bayésienne) (Bayesian inference)
L’estimation bayésienne (Bayesian Inference) est une méthode statistique qui met à jour la probabilité d’une hypothèse à mesure que de nouvelles données ou informations deviennent disponibles.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Elle est basée sur le théorème de Bayes et est utilisée en IA pour le raisonnement incertain et l’apprentissage à partir de données.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Estimation de poses (Pose estimation),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’estimation de poses (Pose Estimation) est une tâche de vision par ordinateur qui consiste à détecter et à localiser les points clés du corps (articulations),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"d’une ou plusieurs personnes (ou objets) dans une image ou une vidéo, afin de déterminer leur posture ou leur orientation.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Éthique de l’IA (AI ethics),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’éthique de l’IA (AI Ethics) est une branche de l’éthique appliquée qui examine les implications morales et sociétales de l’intelligence artificielle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle aborde des questions telles que la partialité (biais), la responsabilité, la transparence, la vie privée, l’équité, et l’impact de l’IA sur l’emploi et la société.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Explicabilité (IA) (XAI,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Explainable,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’explicabilité en IA (Explainable AI ou XAI),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
désigne la capacité d’un système d’intelligence artificielle à fournir des explications compréhensibles par l’homme sur la manière dont il parvient à ses décisions ou prédictions.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est crucial pour la confiance, la détection des biais, et la validation des modèles, en particulier pour les modèles complexes comme les réseaux de neurones profonds.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Extraction de caractéristiques (Feature extraction),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’extraction de caractéristiques (Feature Extraction),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"est le processus de transformation des données brutes en un ensemble de caractéristiques (features) plus informatives et moins redondantes, qui représentent mieux le problème sous-jacent et facilitent l’apprentissage par le modèle d’IA.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Few shot learning,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le Few-Shot Learning est une approche d’apprentissage automatique où un modèle est capable d’apprendre et de généraliser à partir d’un très petit nombre d’exemples d’entraînement (parfois un seul, on parle alors de « one-shot learning », ou même zéro, « zero-shot learning »).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est particulièrement utile lorsque les données étiquetées sont rares ou coûteuses à obtenir.
Fonction d’activation (Activation function)
Dans un neurone artificiel, la fonction d’activation (Activation Function) détermine la sortie de ce neurone en fonction de la somme pondérée de ses entrées.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle introduit des non-linéarités dans le réseau, ce qui permet aux modèles d’apprendre des relations complexes dans les données.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Des exemples courants incluent ReLU, Sigmoid, et Tanh.
Fonction de perte (ou Fonction de coût) (Loss function / Cost function)
La fonction de perte (Loss Function), ou fonction de coût, mesure l’écart entre les prédictions d’un modèle d’IA et les valeurs réelles (vérité terrain) dans l’ensemble de données d’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’objectif du processus d’entraînement est de minimiser cette fonction de perte en ajustant les paramètres du modèle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Forêts aléatoires (Random forests)
Les forêts aléatoires (Random Forests) sont un algorithme d’apprentissage automatique de type ensembliste.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elles construisent une multitude d’arbres de décision pendant l’entraînement et produisent une prédiction qui est la moyenne (pour la régression) ou le mode (pour la classification) des prédictions de chaque arbre individuel, améliorant ainsi la robustesse et la précision.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Frontier AI (Modèles frontières) (Frontier AI),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le terme Frontier AI (ou modèles frontières) désigne les modèles d’intelligence artificielle les plus avancés et les plus performants à un instant T, souvent des modèles de fondation à très grande échelle.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ils repoussent les limites des capacités actuelles de l’IA et peuvent présenter des capacités émergentes et potentiellement des risques spécifiques liés à leur puissance.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Gabarit facial (Facial template)
Un gabarit facial (facial template) est une représentation numérique compacte et distinctive des caractéristiques d’un visage, extraite à partir d’une image ou d’une vidéo par un système de reconnaissance faciale.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ce gabarit, et non l’image brute, est ensuite utilisé pour comparer des visages et identifier des individus.
General",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
purpose,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
AI (GPAI),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
/ Système d’IA à but général (General-purpose,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"system)
Un système d’IA à but général (General-Purpose AI",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
System ou GPAI,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
est un modèle d’IA conçu pour être polyvalent et adaptable à un large éventail d’,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
applications,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
ou de tâches pour lesquelles il n’a pas été spécifiquement développé au départ,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ce terme est notamment utilisé dans des contextes réglementaires (par exemple, l’AI Act européen).
Gradient
En mathématiques, le gradient est un vecteur qui indique la direction et la magnitude de la plus forte pente (augmentation) d’une fonction en un point donné.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"En apprentissage automatique, le gradient de la fonction de perte est utilisé par des algorithmes d’optimisation (comme la descente de gradient) pour ajuster les paramètres du modèle afin de minimiser cette perte.
Grand modèle de langage (LLM – Large language model)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un grand modèle de langage (Large Language Model ou LLM) est un type de modèle de langage basé sur des architectures de réseaux de neurones profonds (souvent des Transformers) et entraîné sur d’énormes quantités de données textuelles.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les LLM sont capables de comprendre, de générer, de traduire et de manipuler le langage humain avec une fluidité et une cohérence remarquables.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Hallucination (IA) (AI Hallucination),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Une hallucination en IA (AI Hallucination) se produit lorsqu’un modèle d’intelligence artificielle, en particulier une IA générative, produit des informations incorrectes, trompeuses, absurdes ou non fondées sur les données d’entraînement, tout en les présentant comme factuelles ou pertinentes.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ce phénomène souligne les limites de la compréhension réelle des modèles.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Hyperparamètre (IA) (Hyperparameter),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un hyperparamètre (hyperparameter) en IA est un paramètre de configuration dont la valeur est fixée avant le début du processus d’entraînement du modèle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Contrairement aux paramètres du modèle qui sont appris pendant l’entraînement, les hyperparamètres (par exemple, le taux d’apprentissage, le nombre de couches dans un réseau de neurones) sont définis par le concepteur et influencent la manière dont le modèle apprend.
IA agentique (Agentic AI)
L’IA agentique (Agentic AI) fait référence à des systèmes d’intelligence artificielle capables de percevoir leur environnement, de raisonner, de planifier et d’agir de manière autonome pour atteindre des objectifs complexes, potentiellement en utilisant des outils externes ou en collaborant avec d’autres agents.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ils possèdent un certain degré d’initiative et d’adaptabilité.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
IA autonome (Autonomous AI),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’IA autonome (Autonomous AI) décrit des systèmes d’intelligence artificielle capables de fonctionner et de prendre des décisions sans intervention humaine directe, sur la base de leurs perceptions, de leurs connaissances et de leurs objectifs.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ce concept est souvent lié à l’IA agentique.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
IA faible (ou IA,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"étroite / Narrow AI) (Weak AI / Narrow AI)
L’IA faible, ou IA",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
étroite (Narrow AI),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
", désigne des systèmes d’intelligence artificielle spécialisés dans l’accomplissement d’une tâche spécifique ou d’un ensemble limité de tâches (par exemple, la reconnaissance faciale, la traduction automatique, la conduite d’un véhicule dans des conditions définies).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est la forme d’IA la plus répandue aujourd’hui.
IA forte (ou IA générale / AGI",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Artificial general intelligence) (Strong AI / Artificial general intelligence)
L’IA forte, ou intelligence artificielle générale (Artificial General Intelligence ou AGI)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
", est un type hypothétique d’IA qui posséderait des capacités cognitives comparables, voire supérieures, à celles d’un être humain.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Une AGI serait capable de comprendre, d’apprendre et d’appliquer son intelligence à n’importe quelle tâche",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"un humain peut accomplir, et de transférer ses connaissances entre des domaines variés.
IA générative (Generative AI)
L’IA générative (Generative AI) regroupe des techniques d’intelligence artificielle capables de créer du nouveau contenu original (texte, images, audio, vidéo, code) à partir de données d’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ces modèles apprennent les motifs et la structure des données d’entrée pour ensuite générer de nouvelles instances plausibles.
IA surhumaine (Superhuman AI)
L’IA surhumaine (Superhuman AI)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"est un concept décrivant une intelligence artificielle qui dépasse les capacités humaines dans une ou plusieurs tâches spécifiques, voire de manière générale dans le cas d’une superintelligence (une AGI ayant largement surpassé l’intelligence humaine).
Inférence (IA) (Processus d’utilisation du modèle) (Inference)
L’inférence (Inference) en IA est le processus qui consiste à utiliser un modèle d’IA déjà entraîné pour faire des prédictions, des classifications ou générer des résultats à partir de nouvelles données d’entrée qu’il n’a jamais vues auparavant.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
C’est la phase de « production » ou d' »utilisation » du modèle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Pour faire de l’inférence facilement en local, je vous conseille d’aller voir cet article qui liste les meilleurs logiciels pour utiliser l’IA.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Infection (d’IA) (AI infection),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’infection d’un système d’IA est un type d’attaque où l’attaquant cherche à contaminer les données d’entraînement ou le modèle lui-même pour en altérer le comportement, souvent de manière discrète, afin qu’il produise des résultats erronés ou malveillants dans certaines conditions spécifiques.
Intelligence artificielle (IA) (Artificial intelligence – AI)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’intelligence artificielle (IA) est un vaste champ de l’informatique qui vise à créer des machines et des logiciels capables de simuler des aspects de l’intelligence humaine, tels que l’apprentissage, le raisonnement, la perception, la résolution de problèmes, la compréhension du langage et la prise de décision",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
".
Internet des objets (IoT) (pour l’IA) (Internet of things)
L’Internet des Objets (Internet of Things ou IoT) désigne le réseau d’objets physiques (appareils, véhicules, capteurs) interconnectés et capables de collecter et d’échanger des données.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Pour l’IA, l’IoT est une source massive de données en temps réel qui peuvent être analysées pour optimiser des processus, faire des prédictions et permettre des interactions intelligentes.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Jailbreak (IA),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un jailbreak en IA est une technique ou une série de prompts conçus pour contourner les restrictions de sécurité, les filtres de contenu ou les directives éthiques implémentées par les concepteurs d’un modèle d’IA, en particulier les grands modèles de langage, afin de lui faire générer des réponses normalement interdites.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Jumeau numérique (Digital twin)
Un jumeau numérique (Digital Twin) est une réplique virtuelle dynamique d’",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"un objet physique, d’un processus, d’un système ou même d’une personne.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il est alimenté par des données en temps réel et peut intégrer l’IA pour simuler des comportements, prédire des pannes, optimiser des performances ou tester des scénarios sans impacter l’entité réelle.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Labellisation (IA) (Labeling),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
La labellisation (Labeling) est un synonyme d’,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
annotation de données.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle consiste à attribuer des étiquettes informatives (labels) aux données brutes pour indiquer leur contenu, leur catégorie ou leurs caractéristiques, afin de les rendre utilisables pour l’entraînement de modèles d’apprentissage supervisé.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Longtermisme (Longtermism),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le longtermisme (Longtermism) est une perspective éthique qui accorde une importance morale significative au bien-être des générations futures, potentiellement sur de très longues échelles de temps.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Dans le contexte de l’IA, cela conduit à se concentrer sur les impacts à long terme et les risques existentiels potentiels de technologies avancées.
Manipulation (d’IA) (AI manipulation)
La manipulation d’un système d’IA désigne une attaque où l’objectif est de tromper ou d’influencer le système pour qu’il accomplisse des tâches ou produise des résultats non prévus ou malveillants, souvent en exploitant ses vulnérabilités ou en introduisant des données d’entrée spécifiquement conçues.
Many",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
shot jailbreak,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le Many,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Shot Jailbreak est une technique de jailbreak spécifique où l’on fournit au modèle d’IA de multiples exemples (des « shots ») de réponses à des questions sensibles ou problématiques, pour l’inciter à continuer sur cette lancée et à répondre à une nouvelle question du même type, contournant ainsi ses garde-fous.
Métavers (et IA) (Metaverse)
Le Métavers (Metaverse) est un concept d’univers virtuel persistant, partagé et tridimensionnel, où les utilisateurs peuvent interagir entre eux et avec des objets numériques via des avatars.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’IA joue un rôle clé dans la création de contenus, la personnalisation des expériences, l’animation des personnages non-joueurs (PNJ) et la modération au sein du métavers.
Modèle (IA) (AI model)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un modèle d’IA (AI Model) est le résultat d’un processus d’entraînement d’un algorithme d’apprentissage automatique sur un ensemble de données.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est une structure mathématique (par exemple, un réseau de neurones avec des poids spécifiques) capable de faire des prédictions, de classifier des informations ou de générer du contenu à partir de nouvelles données d’entrée.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Modèle de fondation (Foundation model),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un modèle de fondation (Foundation Model) est un modèle d’IA à grande échelle, souvent un grand modèle de langage ou un modèle de vision, entraîné sur une très vaste quantité de données non étiquetées.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il acquiert ainsi des capacités générales et peut ensuite être adapté (fine-tuné) à une multitude de tâches spécifiques avec relativement peu de données supplémentaires.
Modèle de langage (Language model)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un modèle de langage (Language Model) est un modèle statistique ou un réseau de neurones entraîné à comprendre et à générer du langage humain.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il apprend les probabilités des séquences de mots (ou de caractères, de tokens) dans une langue donnée, ce qui lui permet de prédire le mot suivant dans une phrase, de générer du texte, de traduire,",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"etc.
Modèle discriminatif (Discriminative model)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un modèle discriminatif, en apprentissage automatique, apprend la frontière de décision entre différentes classes.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il modélise la probabilité conditionnelle P(Y|X), c’est-à-dire la probabilité d’une classe Y étant donné une entrée X. Il est directement utilisé pour des tâches de classification ou de régression.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Modèle génératif (Generative model),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un modèle génératif, par opposition à un modèle discriminatif, apprend la distribution de probabilité conjointe P(X,Y) des données d’entrée X et des étiquettes Y (ou P(X) en apprentissage non supervisé).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Il est capable de générer de nouveaux exemples de données qui ressemblent aux données d’entraînement.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les IA génératives reposent sur ce type de modèles.
Modèle pré-entraîné (Pre-trained model)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un modèle pré-entraîné (Pre-trained Model) est un modèle d’IA qui a déjà été entraîné sur un vaste ensemble de données, souvent pour une tâche générale (comme la reconnaissance d’objets sur ImageNet ou la compréhension du langage sur de grands corpus textuels).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Il peut ensuite être utilisé tel quel ou comme point de départ pour l’apprentissage par transfert sur une tâche plus spécifique.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Moteurs d’inférence (Inference engine),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un moteur d’inférence (Inference Engine) est le composant d’un système expert (ou d’autres systèmes basés sur des règles) qui applique des règles logiques à une base de connaissances pour en déduire de nouvelles informations ou prendre des décisions.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Par extension, le terme peut aussi désigner le logiciel ou matériel optimisé pour exécuter des inférences sur des modèles d’IA (deep learning).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Neurone artificiel (Artificial neuron),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un neurone artificiel (Artificial Neuron), ou perceptron, est l’unité de calcul de base d’un réseau de neurones.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Inspiré du neurone biologique, il reçoit une ou plusieurs entrées pondérées, calcule une somme, et applique une fonction d’activation pour produire une sortie.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
C’est un bloc de construction fondamental des modèles d’apprentissage profond.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Niveaux d’intelligence artificielle (Levels of artificial intelligence),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les niveaux d’intelligence artificielle font référence à des cadres de classification proposés pour évaluer et comparer les capacités des systèmes d’IA, allant de l’absence d’IA à des niveaux surhumains.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ces classifications peuvent distinguer les IA selon leur performance, leur autonomie, leur capacité de généralisation (étroite ou générale) ou leur domaine d’application.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ontologie (Ontology),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"En intelligence artificielle, une ontologie est une représentation formelle et explicite d’un ensemble de concepts, de leurs propriétés et des relations entre eux au sein d’un domaine de connaissance spécifique.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les ontologies aident à structurer l’information, à permettre le raisonnement logique et à faciliter le partage et la réutilisation des connaissances par les systèmes d’IA.
Optimisation (Optimization)
L’optimisation en IA (Optimization) est le processus qui consiste à ajuster les paramètres d’un modèle (ou les hyperparamètres) pour minimiser (ou maximiser) une fonction objectif, typiquement la fonction de perte lors de l’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Des algorithmes comme la descente de gradient sont couramment utilisés pour cela.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Paramètre (Parameter),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Un paramètre (parameter) en IA est une variable interne au modèle dont la valeur est apprise à partir des données pendant le processus d’entraînement.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Par exemple, les poids et les biais dans un réseau de neurones sont des paramètres.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ce sont ces valeurs qui définissent le comportement du modèle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Partitionnement de données (Clustering /,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Regroupement,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Data clustering),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le partitionnement de données, ou clustering (parfois appelé regroupement), est une tâche d’apprentissage non supervisé qui consiste à diviser un ensemble de données en groupes (clusters) de manière à ce que les données au sein d’un même groupe soient plus similaires entre elles qu’avec celles des autres groupes, selon des critères de similarité définis.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Perroquets stochastiques (Stochastic parrots),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’expression «,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
perroquets stochastiques » (Stochastic Parrots) est une métaphore critique utilisée pour décrire les grands modèles de langage.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle suggère que ces modèles, bien que capables de générer du texte grammaticalement correct et contextuellement plausible, ne comprennent pas réellement le sens de ce qu’ils produisent, mais se contentent de recombiner des séquences de mots de manière probabiliste, à l’image d’un perroquet qui répète des phrases.
Prédiction (Prediction)
La prédiction en IA (Prediction) est le processus par lequel un modèle d’IA entraîné estime une valeur de sortie (qui peut être une catégorie, un nombre, ou une séquence) pour une nouvelle donnée d’entrée.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
est l’une des tâches principales des systèmes d’apprentissage automatique.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Prompt (Ingénierie de prompt / Prompt engineering),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un prompt est une instruction, une question ou un texte d’amorce fourni en entrée à un modèle d’IA générative (comme un LLM ou un générateur d’images) pour qu’il produise un résultat spécifique.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’ingénierie de prompt (Prompt Engineering) est l’art et la science de concevoir des prompts efficaces pour obtenir les sorties souhaitées du modèle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Prompt injection
La Prompt Injection est un type",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"attaque de sécurité spécifique aux grands modèles de langage où un attaquant insère des instructions malveillantes dans un prompt, souvent de manière cachée ou indirecte (par exemple, via des données externes que le LLM traite).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ces instructions peuvent amener le modèle à ignorer ses directives originales, à révéler des informations sensibles, ou à exécuter des actions non désirées.
Quantification (Optimisation de modèle) (Quantization)
La quantification (Quantization) est une technique d’optimisation de modèle d’IA qui consiste à réduire la précision numérique des poids et/ou des activations du modèle (par exemple, passer de nombres flottants 32 bits à des entiers 8 bits).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Cela permet de réduire la taille du modèle, sa consommation mémoire et d’accélérer son inférence, souvent avec une perte de précision minimale.
Quantization aware training (QAT)
L’entraînement conscient de la quantification (Quantization Aware Training ou QAT) est une technique où la quantification est simulée pendant le processus d’entraînement du modèle.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Cela permet au modèle de s’adapter aux effets de la quantification et de maintenir une meilleure précision par rapport à la quantification post-entraînement.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Post-training quantization (PTQ)
La quantification post-entraînement (Post-Training Quantization ou PTQ) est une technique qui consiste à convertir un modèle d’IA déjà entraîné en une version à plus faible précision numérique, sans avoir besoin de réentraîner le modèle.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est une méthode plus simple et rapide que le QAT, mais peut parfois entraîner une perte de précision plus importante.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Reconnaissance,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"d’entités nommées (NER – Named-entity recognition)
La reconnaissance d’entités nommées (Named-Entity",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Recognition ou NER),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"est une sous-tâche du traitement du langage naturel qui vise à identifier et à catégoriser des entités spécifiques dans un texte, telles que les noms de personnes, d’organisations, de lieux, les dates, les quantités, etc.
Reconnaissance faciale (Facial recognition)
La reconnaissance faciale (Facial Recognition) est une technologie biométrique capable d’identifier ou de vérifier l’identité",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
une personne en analysant les caractéristiques de son visage à partir d’une image numérique ou d’une vidéo.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle utilise des algorithmes d’IA, notamment de deep learning.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Reconnaissance vocale (Speech recognition / Voice,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"recognition)
La reconnaissance vocale (Speech Recognition ou Voice Recognition), ou reconnaissance automatique de la parole (RAP)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
", est une technologie qui permet à un ordinateur de convertir la parole humaine captée par un microphone en texte écrit.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
C’est un composant essentiel de nombreux assistants virtuels et applications contrôlées par la voix.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Réduction de dimension (ou Réduction de dimensionnalité) (Dimensionality reduction)
La réduction de dimension (Dimensionality Reduction) est le processus qui consiste à réduire le nombre de caractéristiques (variables) utilisées pour décrire des données, tout en préservant autant que possible l’information pertinente.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle aide à simplifier les modèles, à réduire le temps de calcul et à éviter le « fléau de la dimensionnalité ».",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Régression (Regression),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
La régression en IA est une tâche d’apprentissage supervisé,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
qui vise à prédire une valeur numérique continue (par opposition à une catégorie discrète en classification).,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Par exemple, prédire le prix d’une maison en fonction de ses caractéristiques, ou la température de demain.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Régurgitation (Regurgitation),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"La régurgitation en IA se produit lorsqu’un modèle génératif reproduit de manière quasi identique ou très similaire des portions de ses données d’entraînement, plutôt que de créer un contenu véritablement nouveau et original.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Cela peut soulever des questions de droits d’auteur et indiquer un surapprentissage.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Réseau de neurones artificiels (RNA / Neural network),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un réseau de neurones artificiels (RNA), ou simplement réseau de neurones (Neural Network), est un modèle de calcul inspiré par la structure et le fonctionnement du cerveau humain.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il est composé de multiples neurones artificiels interconnectés et organisés en couches, capable d’apprendre des relations complexes à partir de données.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"C’est la base de l’apprentissage profond.
Réseaux de neurones convolutifs (CNN –",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Convolutional,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
neural,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
networks,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les réseaux de neurones convolutifs (Convolutional Neural Networks ou CNN) sont un type de réseau de neurones profonds particulièrement efficaces pour l’analyse de données visuelles (images, vidéos).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ils utilisent des opérations de convolution pour extraire hiérarchiquement des caractéristiques des images, comme les contours, les textures, puis des objets plus complexes.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Réseaux de neurones,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
récurrents (RNN –,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Recurrent neural networks)
Les réseaux",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
de neurones récurrents (Recurrent Neural Networks ou RNN),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"sont un type de réseau de neurones conçu pour traiter des données séquentielles, comme le texte, la parole ou les séries temporelles.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ils possèdent des connexions récurrentes (boucles) qui leur permettent de maintenir une « mémoire » des informations précédentes dans la séquence pour influencer le traitement des éléments suivants.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Réseaux génératifs antagonistes (GANs,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
– Generative adversarial networks),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les réseaux génératifs antagonistes (Generative Adversarial Networks ou GANs) sont une architecture d’apprentissage profond composée de deux réseaux de neurones mis en compétition : un générateur qui essaie de créer des données réalistes (par exemple, des images), et un discriminateur qui essaie de distinguer les données réelles des données générées.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Cette compétition améliore la capacité du générateur à produire des sorties de haute qualité.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Risques existentiels (X risk),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Les risques existentiels (souvent abrégés en X-risk) désignent des événements ou des scénarios potentiels qui pourraient entraîner l’extinction de l’humanité ou réduire de manière drastique et permanente son potentiel futur.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Dans le contexte de l’IA, cela fait souvent référence aux dangers potentiels d’une superintelligence non alignée avec les valeurs humaines.
Robotique (AI robotics)
La robotique IA (AI Robotics) est le domaine qui intègre l’intelligence artificielle dans la conception et le fonctionnement des robots.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’IA permet aux robots de percevoir leur environnement, de prendre des décisions autonomes, d’apprendre de leurs expériences, et d’interagir de manière plus intelligente et flexible avec le monde physique et les humains.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Robustesse (Robustness),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"La robustesse en IA (Robustness) désigne la capacité d’un système d’intelligence artificielle à maintenir ses performances et son comportement attendu même en présence de perturbations, de bruits, d’attaques adverses, ou de données d’entrée qui s’écartent légèrement de celles vues pendant l’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Segmentation des données (Data segmentation)
La segmentation des données en IA (Data Segmentation) fait référence à la division d’",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"un ensemble de données en sous-ensembles distincts, généralement pour l’entraînement",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
", la validation et le test des modèles.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Pour les données visuelles, la segmentation d’image consiste à partitionner une image en régions significatives (par exemple, identifier tous les pixels appartenant à une voiture).
Sous-apprentissage (Underfitting)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le sous-apprentissage (Underfitting) se produit lorsqu’un modèle d’IA est trop simple pour capturer la complexité et les motifs sous-jacents des données d’entraînement.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"En conséquence, le modèle performe mal à la fois sur les données d’entraînement et sur de nouvelles données, car il n’a pas appris suffisamment.
Surapprentissage (Overfitting)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le surapprentissage (Overfitting) se produit lorsqu’un modèle d’IA apprend « par cœur » les données d’entraînement, y compris le bruit et les détails spécifiques, au lieu d’apprendre les tendances générales.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"En conséquence, le modèle performe très bien sur les données d’entraînement mais mal sur de nouvelles données non vues, car il a du mal à généraliser.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Système d’IA (AI system)
Un système d’IA (AI System) est un ensemble de logiciels et/ou de matériels qui utilise des techniques d’intelligence artificielle pour effectuer des tâches spécifiques, souvent en imitant des capacités cognitives humaines.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il comprend généralement des algorithmes, des modèles et des données.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Système d’IA à usage général (General-purpose,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
system),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Voir General,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Purpose,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
AI (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
GPAI),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il s’agit d’un système d’IA conçu pour être adaptable à un large éventail d’applications, sans avoir été spécifiquement développé pour chacune d’elles au départ.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Ce terme est souvent utilisé dans un contexte réglementaire.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Système expert (Expert system)
Un système expert (Expert System) est un type précoce de système d’IA conçu pour émuler les capacités de prise de décision d’un expert humain dans un domaine de connaissance restreint et spécifique.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Il utilise une base de connaissances (faits et règles) et un moteur d’inférence pour tirer des conclusions et fournir des recommandations.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Taux d’apprentissage (Learning rate)
Le taux d’apprentissage (Learning Rate) est un hyperparamètre crucial dans l’entraînement des modèles d’IA, en particulier pour les algorithmes basés sur la descente de gradient.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Il contrôle la taille des pas effectués lors de l’ajustement des paramètres du modèle pour minimiser la fonction de perte.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Un taux trop élevé peut empêcher la convergence, tandis qu’un taux trop bas peut ralentir excessivement l’entraînement.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Technosolutionnisme (Technosolutionism),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le technosolutionnisme (Technosolutionism) est la croyance selon laquelle la plupart des problèmes sociaux, économiques ou environnementaux peuvent être résolus principalement par des solutions technologiques avancées, parfois au détriment d’approches politiques, sociales ou comportementales.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Test (AI testing),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le test en IA (AI Testing) est le processus d’,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
évaluation des performances,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
", de la fiabilité, de la robustesse et de la sécurité d’un système",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
intelligence artificielle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il s’appuie sur des ensembles de données de test dédiés (non vus pendant l’entraînement) et peut inclure des tests fonctionnels, des tests de performance, des tests de robustesse face aux attaques, etc.
Traitement automatique de la parole (Speech processing)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le traitement automatique de la parole (Speech Processing) est un domaine de l’IA qui englobe diverses technologies liées à la parole humaine, notamment la reconnaissance vocale (conversion de la parole en texte), la synthèse vocale (conversion du texte en parole), l’identification du locuteur, et l’analyse des émotions dans la voix.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Traitement automatique du langage naturel (TAL / NLP –,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Natural language processing),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Le traitement automatique du langage naturel (TAL ou NLP –,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Natural,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Language Processing),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
est un sous-domaine de l’IA qui se concentre sur l’interaction entre les ordinateurs et le langage humain.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Il vise à permettre aux machines de comprendre, d’interpréter, de générer et de manipuler le langage naturel (parlé ou écrit) de manière utile.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Transformer (Architecture de modèle),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Le Transformer est une architecture de réseau de neurones profond introduite en 2017, qui a révolutionné le traitement du langage naturel et s’est étendue à d’autres domaines (comme la vision par ordinateur).",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Elle repose principalement sur des mécanismes d’attention (en particulier l’auto-attention) pour traiter les dépendances à longue distance dans les séquences de données de manière efficace et parallèle.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
De nombreux grands modèles de langage (LLM) sont basés sur l’architecture Transformer.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Validation (AI validation,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
La validation en IA (AI Validation,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
est le processus qui consiste à évaluer si un modèle d’IA répond aux exigences spécifiées et s’il est apte à l’usage prévu.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle implique généralement de tester le modèle sur un ensemble de données de validation distinct pour ajuster les hyperparamètres et s’assurer de sa capacité de généralisation avant le test final.
Validation croisée (Cross-validation)
La validation croisée (Cross-Validation) est une technique statistique utilisée pour évaluer la performance d’un modèle d’IA et sa capacité à généraliser à de nouvelles données, en particulier lorsque la quantité de données disponibles est limitée.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Elle consiste à diviser l’ensemble de données en plusieurs sous-ensembles (plis), à entraîner le modèle sur certains plis et à le tester sur le pli restant, en répétant ce processus pour chaque pli.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Vérité terrain (Ground truth),https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
La vérité terrain (Ground Truth) désigne l’information considérée comme étant la « bonne réponse » ou la réalité objective pour un ensemble de données.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"En apprentissage supervisé, ce sont les étiquettes correctes fournies avec les données d’entraînement, contre lesquelles les prédictions du modèle sont comparées pour évaluer sa performance.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Vision par ordinateur (,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Computer vision)
La vision par ordinateur (Computer Vision)",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"est un domaine de l’intelligence artificielle qui vise à permettre aux machines d' »interpréter » et de comprendre des informations visuelles à partir d’images numériques ou de vidéos, de manière similaire à la vision humaine.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Les tâches incluent la reconnaissance d’objets, la détection de visages, la segmentation d’images, etc.
Comprendre le vocabulaire de l’IA est une première étape essentielle pour apprécier ses avancées, ses défis et son impact potentiel sur notre avenir.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"L’IA continue de progresser rapidement, et avec elle, son langage.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
Je vous encourage à rester curieux et à poursuivre votre exploration.,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Ce glossaire se veut une ressource vivante, n’hésitez pas à y revenir au gré de vos besoins.",https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
L’aventure de la connaissance en IA ne fait que commencer !,https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/
"Après un précédent article sur les choix à faire dans l’histoire de GTA 4, j’ai décidé de vous proposer un nouveau tutoriel.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Ce tutoriel sur GTA IV a eu pas mal de succès donc je suis très content, je ne pensais pas que ce serait autant le cas pour un si vieux jeu sorti sur PS3 en 2008.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Aujourd’hui, Grand Theft",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Auto IV reste encore de loin mon opus préféré avec son ambiance et son coté sombre, très sérieux.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Je verrais bien si GTA 6 arrive à le surpasser, mais pour ça, il va falloir attendre qui sorte enfin après de très nombreuses années d’attente.
Dans ce tutoriel, on ne va donc pas s’attarder sur l’ancien GTA IV et le futur GTA VI, mais je vais plutôt vous parler de Grand Theft",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Auto V.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Je vais vous expliquer comment gagner beaucoup d’argent et devenir riche à l’aide la bourse via les assassinats commandités par Lester.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Les missions d’assassinat de Lester sont disponibles avec le personnage principal Franklin.
Pour maximiser vos profits boursiers grâce aux missions d’assassinat de Lester, une règle d’or s’impose : la patience.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Le principe fondamental est de terminer l’histoire principale de GTA 5 avant de vous lancer dans la plupart de ces missions.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Pourquoi ?,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Simplement parce que les braquages de l’histoire vous auront rapporté une coquette somme, capital de départ essentiel pour des investissements massifs et donc, des retours sur investissement plus conséquents.
Seule la toute première mission d’assassinat de Lester est obligatoire pour progresser dans le scénario.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Tâchez de la repousser le plus possible dans votre progression afin de disposer d’un capital initial plus important, même si pour celle-ci, les sommes en jeu seront moindres que celles que vous pourrez manipuler post-histoire.
Première mission d’assassinat (Obligatoire) :",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Room service,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Une fois que vous ne pouvez plus la retarder, préparez-vous pour cette première mission avec Franklin.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Avant de la démarrer, prenez le contrôle de vos trois personnages (Franklin, Michael et Trevor) et investissez la totalité de leur argent sur l’action Betta",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Pharmaceuticals (,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
BET sur BAWSAQ),https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Accomplissez la mission.
Par la suite, surveillez le cours de l’action BET.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Lorsque le bénéfice atteint environ 80%, il est temps de vendre.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Mais l’opportunité ne s’arrête pas là.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Le cours de l’action Bilkington (BIL sur LCN) va chuter suite à cette mission.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Attendez qu’il atteigne son point le plus bas (cela peut prendre quelques jours en jeu, surveillez une légère remontée avant une nouvelle baisse), puis investissez.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Soyez patient, car l’action remontera, offrant un potentiel de gain pouvant également avoisiner les 80% après une bonne semaine en jeu.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Une fois cette mission derrière vous, concentrez-vous sur la fin de l’histoire principale.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Ne touchez plus aux autres missions « L » de Lester avant d’avoir vu le générique de fin.
Deuxième mission d’assassinat : Tir groupé
Pour la deuxième mission, la stratégie est double.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Avant de la commencer avec Franklin, investissez l’intégralité des fonds de vos trois personnages sur l’action Debonaire (DEB sur LCN).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Une fois la mission accomplie,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
", retournez à une planque et sauvegardez en dormant deux fois de suite pour faire avancer le temps.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
L’action Debonaire devrait atteindre un pic de profit (souvent autour de 80%).,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Vendez tout.
Immédiatement après avoir vendu vos actions Debonaire, réinvestissez la totalité de vos gains sur l’action Redwood (RWC sur LCN), dont la valeur a chuté suite à vos actions.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Sauvegardez à nouveau deux fois en dormant, puis vendez vos actions Redwood lorsque le profit vous convient.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Troisième mission d’assassinat : Prosti-tueur
Avant de lancer la troisième mission, placez tout l’argent de vos personnages sur l’action Fruit (FRT sur BAWSAQ).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Accomplissez la mission.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Suite à cela, le cours de Fruit va grimper.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Sauvegardez deux fois en dormant à votre planque, puis vendez lorsque vous estimez le profit satisfaisant (autour de 50-51%).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Il est parfois noté qu’une sauvegarde manuelle avant d’atteindre le pic peut aider si le cours semble stagner plus bas.
Fait intéressant, cette mission impacte négativement l’action Facade (FAC sur BAWSAQ).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Une fois que Facade a bien chuté, achetez-en.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Étant sur le BAWSAQ, son cours est influencé par des facteurs qui tendent à le faire remonter à sa valeur initiale après un certain temps (souvent environ deux heures de jeu réel, pas en jeu).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Surveillez-la pour revendre au bon moment, lorsque le cours s’est stabilisé à la hausse.
Quatrième mission d’assassinat : Tickets siouplé
Pour cette mission, l’investissement se fait après l’avoir terminée.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Vos actions vont faire chuter le cours de l’action Vapid (VAP sur BAWSAQ).,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Une fois la mission réussie et que le cours de Vapid est au plus bas, investissez massivement avec vos trois personnages.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Comme pour Facade, l’action Vapid étant sur BAWSAQ, elle devrait remonter à sa valeur d’origine après un délai (environ deux heures de temps réel également).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Soyez patient et revendez pour un joli bénéfice.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Pour avancer le temps en jeu entre-temps, sauvegardez en dormant deux fois.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Cinquième mission d’assassinat :,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Le grand saut
La dernière mission d’assassinat de Lester requiert un investissement avant de la commencer.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Misez tout l’argent de Franklin, Michael et Trevor sur l’action GoldCoast (GCD sur LCN).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Terminez la mission.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Le cours de GoldCoast devrait alors s’envoler.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Sauvegardez en dormant deux fois, puis vendez lorsque le profit vous semble optimal (des gains de plus de 60%, parfois jusqu’à 80%, sont courants).
Quelques astuces pour optimiser cette stratégie : sauvegardez régulièrement, surtout avant chaque mission et avant chaque transaction boursière importante.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Cela vous permettra de recharger si un cours n’évolue pas comme prévu ou si vous manquez un pic.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Soyez patient, les marchés fluctuent.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Utiliser la fonction de sauvegarde rapide via le téléphone est aussi un bon réflexe.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Pour faire avancer le temps en jeu et actualiser les cours sur LCN, sauvegarder en dormant dans une planque est efficace (chaque sauvegarde fait avancer de 6h).",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Pour BAWSAQ, l’actualisation peut prendre un peu plus de temps, parfois lié au temps réel comme indiqué.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"En suivant scrupuleusement ces étapes, il est tout à fait possible de voir la fortune de vos personnages atteindre des sommets, transformant les millions",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
durement gagnés lors des braquages en véritables montagnes d’or.,https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
"Certains joueurs rapportent avoir multiplié leur capital initial par cinq, voire bien plus, atteignant des sommes colossales qui permettent de s’offrir tout ce que Los Santos a à proposer.",https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/
Winget est le gestionnaire de paquets en ligne de commande officiel de Microsoft pour Windows.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Il permet de rationaliser la manière dont les utilisateurs et les administrateurs interagissent avec les logiciels en centralisant leur gestion au sein d’un seul outil dans le terminal.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Bien qu’il ne soit pas le premier outil de ce type, rejoignant des solutions établies comme Chocolatey ou Scoop qui disposent de communautés actives et de vastes répertoires, Winget a pour lui son statut d’outil officiel développé et maintenu par Microsoft.
C’",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
est une bonne nouvelle que Microsoft se mette à pousser ce genre d’outils,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
", je pense notamment à WSL qui permet d’utiliser Linux directement dans Windows.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Cette intégration native dans les versions récentes de Windows 10 et 11 lui confère un avantage structurel : il est disponible sans installation préalable et son développement est aligné sur l’évolution du système d’exploitation, ce qui permet d’automatiser l’installation, la mise à jour et la suppression d’applications tout en offrant une perspective de fiabilité et de compatibilité à long terme.
WinGet s’apparente aussi à ce que l’on retrouve sur macOS avec brew ou encore les différents gestionnaires de paquets sous les distributions.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Le plus connu étant surement,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Advanced,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Packaging,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Tool ou APT,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"sur la distribution Linux Ubuntu.
Pour commencer",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
", la première action consiste souvent à vérifier si une application est disponible dans les dépôts de Winget.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
La commande de recherche permet de trouver des paquets en se basant sur leur nom ou des mots-clés.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Cette étape est utile pour confirmer l’orthographe exacte ou l’identifiant (ID) unique d’,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
un paquet avant de l’installer.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
winget search,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"""nom de l'application""",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Dans mon cas, je souhaite en savoir plus sur VLC, je fais donc la commande toute simple :
winget search ""vlc""
Elle me retourne alors le résultat suivant :",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Une fois le logiciel identifié, son installation s’effectue avec la commande install.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Pour une précision accrue et pour éviter toute confusion avec des paquets aux noms similaires, il est conseillé d’utiliser l’ID du paquet, que la commande search aura retourné dans les résultats.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"winget install ""nom ou ID de l'application""",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"En me basant sur les informations récupérées juste avant, je lance donc la commande suivante pour installer VLC avec WinGet :
winget install ""VideoLAN.VLC""
WinGet s’occupe de tout et pour certains programmes et application, il faudra tout de même valider une UAC d’administrateur pour finir l’installation.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Pour éviter cela, vous pouvez lancer le terminal en tant qu’administrateur en passant par « Exécuter en tant qu’administrateur » et le programme s’installera sans vous demander de validation.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Si vous retentez de relancer la même commande, il sait évidemment que c’est déjà bien installé donc ne fera rien de plus.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Au-delà de l’ajout de programmes, il est souvent nécessaire d’avoir une vue d’ensemble des applications déjà présentes sur le système.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
La commande list remplit cette fonction en affichant un inventaire de tous les logiciels que Winget peut reconnaître sur la machine.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"winget list
La maintenance des applications est un aspect fondamental de l’hygiène informatique.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Winget simplifie la mise à jour des logiciels en identifiant ceux pour lesquels une nouvelle version est disponible.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Vous pouvez cibler une application spécifique pour la mettre à niveau.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"winget upgrade ""nom ou ID de l'application""",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Pour une gestion encore plus efficace, il est possible de lancer la mise à jour de tous les paquets obsolètes en une seule commande.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Cette fonctionnalité est particulièrement pratique pour maintenir l’ensemble de son parc logiciel à jour sans effort manuel.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
winget upgrade --all,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
Le cycle de vie d’un logiciel inclut aussi sa suppression.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
La désinstallation d’une application via Winget est aussi directe que son installation.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
L’utilisation de l’ID du paquet est là encore une bonne pratique,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
pour s’assurer de retirer le bon programme.,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"winget uninstall ""nom ou ID de l'application""",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"Pour les utilisateurs souhaitant répliquer une configuration logicielle sur plusieurs postes ou simplement sauvegarder leur liste d’applications, Winget propose des fonctions d’exportation et d’importation.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"La première commande crée un fichier contenant la liste des paquets installés, qui peut ensuite être utilisé par la seconde pour automatiser l’installation de ces mêmes paquets sur une autre machine.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
winget export,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"""C:\chemin\vers\votre\fichier.json""
winget",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
import,https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
"-i ""C:\chemin\vers\votre\fichier.json""
Voilà, vous avez maintenant les bases de l’utilisation de WinGet sous Windows pour gérer l’ensemble ou presque de vos programmes à l’aide du terminal.",https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/
