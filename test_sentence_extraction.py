#!/usr/bin/env python3
"""
Script de test pour comparer les différentes méthodes d'extraction de phrases.
"""

from html_text_extractor import (
    extract_sentences_nltk,
    extract_sentences_regex_improved,
    extract_sentences_spacy,
    extract_sentences_regex_original,
    extract_sentences,
)


def test_sentence_extraction():
    """
    Teste les différentes méthodes d'extraction de phrases avec des cas complexes.
    """

    # Texte de test avec différents cas difficiles
    test_text = """
    Bonjour Dr. Martin. Comment allez-vous aujourd'hui ?
    J'espère que tout va bien ! Le prix est de 19.99€.
    Voici une citation : "La vie est belle." Et voici une autre phrase.
    <PERSON><PERSON> a dit : "Nous partirons à 15h30." C'est parfait !
    Les abréviations comme etc. peuvent poser problème.
    Que pensez-vous de cela ? Vraiment intéressant...
    """

    print("=" * 60)
    print("TEXTE DE TEST:")
    print("=" * 60)
    print(test_text.strip())
    print("\n")

    methods = [
        ("NLTK (Recommandée)", "nltk"),
        ("Regex Améliorée", "regex_improved"),
        ("Regex Originale", "regex"),
        ("spaCy", "spacy"),
    ]

    results = {}

    for method_name, method_key in methods:
        print("=" * 60)
        print(f"MÉTHODE: {method_name}")
        print("=" * 60)

        try:
            if method_key == "spacy":
                sentences = extract_sentences_spacy(test_text)
            elif method_key == "nltk":
                sentences = extract_sentences_nltk(test_text)
            elif method_key == "regex_improved":
                sentences = extract_sentences_regex_improved(test_text)
            elif method_key == "regex":
                sentences = extract_sentences_regex_original(test_text)

            results[method_name] = sentences

            print(f"Nombre de phrases extraites: {len(sentences)}")
            print("\nPhrases extraites:")
            for i, sentence in enumerate(sentences, 1):
                print(f"{i:2d}. {sentence}")

        except Exception as e:
            print(f"Erreur avec la méthode {method_name}: {e}")
            results[method_name] = []

        print("\n")

    # Comparaison des résultats
    print("=" * 60)
    print("COMPARAISON DES RÉSULTATS")
    print("=" * 60)

    for method_name, sentences in results.items():
        print(f"{method_name}: {len(sentences)} phrases")

    return results


def benchmark_methods():
    """
    Compare les performances des différentes méthodes.
    """
    import time

    # Texte plus long pour le benchmark
    long_text = (
        """
    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
    Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
    Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum.
    Excepteur sint occaecat cupidatat non proident, sunt in culpa qui.
    """
        * 100
    )  # Répéter 100 fois pour avoir un texte conséquent

    methods = [
        ("NLTK", extract_sentences_nltk),
        ("Regex Améliorée", extract_sentences_regex_improved),
        ("Regex Originale", extract_sentences_regex_original),
    ]

    print("=" * 60)
    print("BENCHMARK DES PERFORMANCES")
    print("=" * 60)
    print(f"Texte de test: {len(long_text)} caractères")
    print()

    for method_name, method_func in methods:
        start_time = time.time()

        # Exécuter 10 fois pour avoir une moyenne
        for _ in range(10):
            sentences = method_func(long_text)

        end_time = time.time()
        avg_time = (end_time - start_time) / 10

        print(f"{method_name:20s}: {avg_time:.4f}s (moyenne sur 10 exécutions)")
        print(f"                     {len(sentences)} phrases extraites")
        print()


def test_edge_cases():
    """
    Teste des cas particuliers et difficiles.
    """
    edge_cases = [
        ("Texte vide", ""),
        ("Une seule phrase", "Ceci est une phrase simple."),
        ("Sans ponctuation", "Ceci est un texte sans ponctuation finale"),
        (
            "Abréviations",
            "Dr. Martin et M. Dupont se sont rencontrés. Ils ont discuté.",
        ),
        ("Nombres décimaux", "Le prix est 19.99€. C'est cher ! Que faire ?"),
        ("Guillemets", 'Il a dit : "Bonjour !" puis il est parti.'),
        ("Points de suspension", "Je ne sais pas... Peut-être que... Non."),
        ("Majuscules manquantes", "première phrase. deuxième phrase sans majuscule."),
        ("Phrases très courtes", "Oui. Non. Peut-être. OK."),
    ]

    print("=" * 60)
    print("TEST DES CAS PARTICULIERS")
    print("=" * 60)

    for case_name, test_text in edge_cases:
        print(f"\n{case_name}:")
        print(f"Texte: '{test_text}'")

        # Tester avec NLTK (méthode recommandée)
        sentences = extract_sentences_nltk(test_text)
        print(f"NLTK: {len(sentences)} phrases -> {sentences}")

        # Tester avec regex améliorée
        sentences_regex = extract_sentences_regex_improved(test_text)
        print(f"Regex: {len(sentences_regex)} phrases -> {sentences_regex}")


if __name__ == "__main__":
    print("🔍 TEST DES MÉTHODES D'EXTRACTION DE PHRASES")
    print("=" * 60)

    # Test principal
    test_sentence_extraction()

    # Test des cas particuliers
    test_edge_cases()

    # Benchmark des performances
    benchmark_methods()

    print("\n" + "=" * 60)
    print("RECOMMANDATIONS:")
    print("=" * 60)
    print("1. NLTK: Meilleur équilibre précision/performance pour la plupart des cas")
    print("2. spaCy: Excellent pour l'analyse avancée (si installé)")
    print("3. Regex améliorée: Bonne alternative sans dépendances externes")
    print("4. Regex simple: À éviter sauf pour des cas très simples")
